// Payment Page JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Sample order data - in real application, this would come from cart/session
    const orderData = {
        items: [
            {
                id: 1,
                name: "Website Development Package",
                description: "Complete e-commerce website with admin panel",
                quantity: 1,
                rate: 2500.00,
                icon: "fas fa-laptop-code"
            },
            {
                id: 2,
                name: "GPS Tracker Pro - Annual Plan",
                description: "Premium vehicle tracking with advanced features",
                quantity: 2,
                rate: 299.99,
                icon: "fas fa-map-marker-alt"
            },
            {
                id: 3,
                name: "Business Hosting - Premium",
                description: "High-performance hosting with SSL certificate",
                quantity: 1,
                rate: 199.99,
                icon: "fas fa-server"
            }
        ],
        taxRate: 0.18, // 18% GST
        discount: 0,
        currency: "$"
    };

    // Initialize payment page
    initializePaymentPage();

    function initializePaymentPage() {
        renderOrderItems();
        calculateTotals();
        setupPaymentMethods();
        generateUPIQRCode();
        setupEventListeners();
    }

    function renderOrderItems() {
        const orderItemsContainer = document.getElementById('orderItems');
        orderItemsContainer.innerHTML = '';

        orderData.items.forEach((item, index) => {
            const itemElement = createOrderItemElement(item, index);
            orderItemsContainer.appendChild(itemElement);
        });
    }

    function createOrderItemElement(item, index) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'order-item';
        itemDiv.innerHTML = `
            <div class="item-image">
                <i class="${item.icon}"></i>
            </div>
            <div class="item-details">
                <div class="item-name">${item.name}</div>
                <div class="item-description">${item.description}</div>
                <div class="item-quantity">
                    <span>Quantity:</span>
                    <div class="quantity-controls">
                        <button class="quantity-btn" onclick="updateQuantity(${index}, -1)">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="quantity-input" value="${item.quantity}" 
                               onchange="updateQuantityInput(${index}, this.value)" min="1">
                        <button class="quantity-btn" onclick="updateQuantity(${index}, 1)">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="item-pricing">
                <div class="item-rate">${orderData.currency}${item.rate.toFixed(2)} each</div>
                <div class="item-total">${orderData.currency}${(item.quantity * item.rate).toFixed(2)}</div>
            </div>
        `;
        return itemDiv;
    }

    function calculateTotals() {
        const subtotal = orderData.items.reduce((sum, item) => sum + (item.quantity * item.rate), 0);
        const tax = subtotal * orderData.taxRate;
        const total = subtotal + tax - orderData.discount;

        // Update all total displays
        updateTotalDisplays(subtotal, tax, total);
        
        // Update UPI amount
        updateUPIAmount(total);
    }

    function updateTotalDisplays(subtotal, tax, total) {
        const elements = {
            subtotalAmount: subtotal,
            taxAmount: tax,
            finalAmount: total,
            summarySubtotal: subtotal,
            summaryTax: tax,
            summaryTotal: total
        };

        Object.entries(elements).forEach(([id, amount]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = `${orderData.currency}${amount.toFixed(2)}`;
            }
        });

        // Update item count
        const totalItems = orderData.items.reduce((sum, item) => sum + item.quantity, 0);
        const summaryItemsElement = document.getElementById('summaryItems');
        if (summaryItemsElement) {
            summaryItemsElement.textContent = totalItems;
        }

        // Show/hide discount row
        const discountRow = document.getElementById('discountRow');
        if (orderData.discount > 0) {
            discountRow.style.display = 'flex';
            document.getElementById('discountAmount').textContent = `-${orderData.currency}${orderData.discount.toFixed(2)}`;
        } else {
            discountRow.style.display = 'none';
        }
    }

    function updateUPIAmount(total) {
        // Convert to INR for UPI (assuming 1 USD = 83 INR for demo)
        const inrAmount = (total * 83).toFixed(2);
        const upiAmountElement = document.getElementById('upiAmount');
        if (upiAmountElement) {
            upiAmountElement.textContent = `₹${inrAmount}`;
        }
        
        // Update QR code with new amount
        generateUPIQRCode(inrAmount);
    }

    function setupPaymentMethods() {
        const paymentMethods = document.querySelectorAll('.payment-method');
        const upiSection = document.getElementById('upiSection');

        paymentMethods.forEach(method => {
            method.addEventListener('click', function() {
                // Remove active class from all methods
                paymentMethods.forEach(m => m.classList.remove('active'));

                // Add active class to clicked method
                this.classList.add('active');

                // Update radio button
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;

                // Show/hide UPI section and update button text
                const methodType = this.dataset.method;
                const confirmBtn = document.getElementById('confirmPaymentBtn');

                if (methodType === 'upi') {
                    upiSection.style.display = 'block';
                    confirmBtn.innerHTML = '<i class="fas fa-lock"></i> Confirm UPI Payment';
                } else {
                    upiSection.style.display = 'none';
                    confirmBtn.innerHTML = '<i class="fas fa-lock"></i> Proceed to Payment';
                }

                // Clear any existing validation errors when switching methods
                clearFieldError('transactionReference');
            });
        });
    }

    function generateUPIQRCode(amount) {
        const qrCodeContainer = document.getElementById('qrCode');
        const upiId = 'digitalace@paytm';
        const merchantName = 'DigitalAce Solutions';
        const transactionRef = document.getElementById('upiReference').textContent;
        
        // UPI URL format
        const upiUrl = `upi://pay?pa=${upiId}&pn=${encodeURIComponent(merchantName)}&am=${amount || '0'}&cu=INR&tn=${encodeURIComponent(transactionRef)}`;
        
        // Generate QR code using a QR code library or service
        // For demo purposes, we'll show a placeholder
        qrCodeContainer.innerHTML = `
            <div class="qr-placeholder">
                <i class="fas fa-qrcode"></i>
                <p>QR Code for ₹${amount || '0'}</p>
                <small style="font-size: 0.7rem; color: var(--text-secondary);">
                    Scan with any UPI app
                </small>
            </div>
        `;
        
        // In a real application, you would use a QR code library like qrcode.js:
        // QRCode.toCanvas(qrCodeContainer, upiUrl, function (error) {
        //     if (error) console.error(error);
        // });
    }

    function setupEventListeners() {
        // Confirm payment button
        const confirmPaymentBtn = document.getElementById('confirmPaymentBtn');
        confirmPaymentBtn.addEventListener('click', function() {
            const selectedMethod = document.querySelector('input[name="paymentMethod"]:checked').value;

            // Validate payment before processing
            if (validatePayment(selectedMethod)) {
                processPayment(selectedMethod);
            }
        });

        // Edit cart button
        const editCartBtn = document.getElementById('editCartBtn');
        editCartBtn.addEventListener('click', function() {
            window.history.back();
        });

        // Transaction reference input validation
        const transactionReference = document.getElementById('transactionReference');
        if (transactionReference) {
            // Real-time validation
            transactionReference.addEventListener('input', function() {
                validateTransactionReference(this.value.trim());
            });

            // Clear error on focus
            transactionReference.addEventListener('focus', function() {
                clearFieldError('transactionReference');
            });

            // Format input (remove special characters, limit length)
            transactionReference.addEventListener('input', function() {
                // Allow only alphanumeric characters and common UPI reference formats
                this.value = this.value.replace(/[^a-zA-Z0-9\-_]/g, '');
            });
        }

        // Mobile-specific improvements
        setupMobileOptimizations();
    }

    function setupMobileOptimizations() {
        // Improve touch interactions on mobile
        if (window.innerWidth <= 768) {
            // Add touch feedback for buttons
            const buttons = document.querySelectorAll('.btn, .quantity-btn, .payment-method');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });

                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 100);
                });
            });

            // Smooth scroll to payment section when method is selected
            const paymentMethods = document.querySelectorAll('.payment-method');
            paymentMethods.forEach(method => {
                method.addEventListener('click', function() {
                    if (this.dataset.method === 'upi') {
                        setTimeout(() => {
                            const upiSection = document.getElementById('upiSection');
                            upiSection.scrollIntoView({
                                behavior: 'smooth',
                                block: 'nearest'
                            });
                        }, 300);
                    }
                });
            });

            // Auto-scroll to show updated totals after quantity change
            window.scrollToTotals = function() {
                setTimeout(() => {
                    const totalsSection = document.querySelector('.order-totals');
                    if (totalsSection) {
                        totalsSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }
                }, 200);
            };
        }

        // Handle orientation changes
        window.addEventListener('orientationchange', function() {
            setTimeout(() => {
                // Recalculate layout after orientation change
                window.scrollTo(0, 0);
            }, 500);
        });

        // Prevent zoom on input focus (iOS)
        const inputs = document.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                if (window.innerWidth <= 768) {
                    const viewport = document.querySelector('meta[name="viewport"]');
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }
            });

            input.addEventListener('blur', function() {
                if (window.innerWidth <= 768) {
                    const viewport = document.querySelector('meta[name="viewport"]');
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
                }
            });
        });
    }

    function validatePayment(method) {
        let isValid = true;

        // For UPI payments, transaction reference is mandatory
        if (method === 'upi') {
            const transactionRef = document.getElementById('transactionReference');
            const refValue = transactionRef ? transactionRef.value.trim() : '';

            if (!refValue) {
                showFieldError('transactionReference', 'Transaction reference number is required to confirm payment');
                isValid = false;
            } else if (refValue.length < 8) {
                showFieldError('transactionReference', 'Transaction reference must be at least 8 characters long');
                isValid = false;
            } else {
                clearFieldError('transactionReference');
            }
        }

        // Validate billing form
        const billingForm = document.getElementById('billingForm');
        const requiredFields = billingForm.querySelectorAll('[required]');

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('error');
                isValid = false;
            } else {
                field.classList.remove('error');
                field.classList.add('success');
            }
        });

        if (!isValid) {
            // Scroll to first error
            const firstError = document.querySelector('.error, .error-message:not([style*="display: none"])');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        return isValid;
    }

    function validateTransactionReference(value) {
        if (!value) {
            showFieldError('transactionReference', 'Transaction reference number is required');
            return false;
        } else if (value.length < 8) {
            showFieldError('transactionReference', 'Transaction reference must be at least 8 characters long');
            return false;
        } else if (value.length > 50) {
            showFieldError('transactionReference', 'Transaction reference is too long');
            return false;
        } else {
            clearFieldError('transactionReference');
            return true;
        }
    }

    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const errorElement = document.getElementById(fieldId.replace('transaction', '') + 'referenceError');

        if (field) {
            field.classList.add('error');
            field.classList.remove('success');
        }

        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'flex';
        }
    }

    function clearFieldError(fieldId) {
        const field = document.getElementById(fieldId);
        const errorElement = document.getElementById(fieldId.replace('transaction', '') + 'referenceError');

        if (field) {
            field.classList.remove('error');
            if (field.value.trim()) {
                field.classList.add('success');
            }
        }

        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    function processPayment(method) {
        // Show loading state
        const confirmBtn = document.getElementById('confirmPaymentBtn');
        const originalText = confirmBtn.innerHTML;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        confirmBtn.disabled = true;

        // Get transaction reference for UPI payments
        const transactionRef = method === 'upi' ?
            document.getElementById('transactionReference').value.trim() :
            'N/A';

        // Simulate payment processing
        setTimeout(() => {
            // Reset button
            confirmBtn.innerHTML = originalText;
            confirmBtn.disabled = false;

            // Show success message
            showPaymentSuccess(method, transactionRef);
        }, 2000);
    }

    function showPaymentSuccess(method, transactionRef = 'N/A') {
        const total = orderData.items.reduce((sum, item) => sum + (item.quantity * item.rate), 0);
        const tax = total * orderData.taxRate;
        const finalAmount = total + tax - orderData.discount;

        let successMessage = `Payment Successful!\n\nMethod: ${method.toUpperCase()}\nAmount: ${orderData.currency}${finalAmount.toFixed(2)}`;

        if (method === 'upi' && transactionRef !== 'N/A') {
            successMessage += `\nTransaction Reference: ${transactionRef}`;
        }

        successMessage += `\n\nOrder confirmation will be sent to your email.`;

        alert(successMessage);

        // Store payment details for confirmation page (optional)
        localStorage.setItem('lastPayment', JSON.stringify({
            method: method,
            amount: finalAmount,
            transactionRef: transactionRef,
            timestamp: new Date().toISOString(),
            orderRef: document.getElementById('upiReference').textContent
        }));

        // Redirect to success page or dashboard
        window.location.href = 'index.html';
    }

    // Global functions for quantity controls
    window.updateQuantity = function(index, change) {
        const newQuantity = orderData.items[index].quantity + change;
        if (newQuantity >= 1) {
            orderData.items[index].quantity = newQuantity;
            renderOrderItems();
            calculateTotals();

            // Mobile optimization: scroll to show updated totals
            if (window.innerWidth <= 768 && window.scrollToTotals) {
                window.scrollToTotals();
            }
        }
    };

    window.updateQuantityInput = function(index, value) {
        const newQuantity = parseInt(value);
        if (newQuantity >= 1) {
            orderData.items[index].quantity = newQuantity;
            calculateTotals();

            // Mobile optimization: scroll to show updated totals
            if (window.innerWidth <= 768 && window.scrollToTotals) {
                window.scrollToTotals();
            }
        } else {
            // Reset to previous value if invalid
            renderOrderItems();
        }
    };

    // Generate random order reference
    function generateOrderReference() {
        const timestamp = Date.now().toString().slice(-6);
        return `ORD-2023-${timestamp}`;
    }

    // Set initial order reference
    document.getElementById('upiReference').textContent = generateOrderReference();
});
