<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - DigitalAce</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Responsive Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            <button class="toggle-sidebar" id="toggleSidebar">
                <i class="fas fa-bars"></i>
            </button>
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                <span>DigitalAce</span>
            </a>
        </div>
        
        <div class="nav-buttons">
            <button class="nav-btn user-btn">
                <i class="fas fa-user-circle"></i>
                <span>John Doe</span>
            </button>
            <button class="theme-toggle" id="themeToggle">
                <i class="fas fa-moon"></i>
            </button>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>
    </nav>

    <!-- Dashboard Layout -->
    <div class="dashboard-container" id="dashboardContainer">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User">
                <div class="user-info">
                    <div class="user-name">John Doe</div>
                    <div class="user-email"><EMAIL></div>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Main</div>
                <ul class="nav-links">
                    <li>
                        <a href="index.html">
                            <i class="fas fa-home"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html">
                            <i class="fas fa-cogs"></i>
                            <span class="nav-text">My Services</span>
                            <span class="badge">3</span>
                        </a>
                    </li>
                    <li>
                        <a href="purchases.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="nav-text">My Purchases</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">GPS Tracking</div>
                <ul class="nav-links">
                    <li>
                        <a href="gps-devices.html">
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="nav-text">My Devices</span>
                            <span class="badge">2</span>
                        </a>
                    </li>
                    <li>
                        <a href="gps-plans.html">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span class="nav-text">Plans & Billing</span>
                        </a>
                    </li>
                    <li>
                        <a href="gps-credentials.html">
                            <i class="fas fa-key"></i>
                            <span class="nav-text">Credentials</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Hosting</div>
                <ul class="nav-links">
                    <li>
                        <a href="hosting-accounts.html">
                            <i class="fas fa-server"></i>
                            <span class="nav-text">My Hosting</span>
                            <span class="badge">1</span>
                        </a>
                    </li>
                    <li>
                        <a href="hosting-plans.html">
                            <i class="fas fa-box-open"></i>
                            <span class="nav-text">Hosting Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="hosting-billing.html">
                            <i class="fas fa-credit-card"></i>
                            <span class="nav-text">Billing</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Account</div>
                <ul class="nav-links">
                    <li>
                        <a href="settings.html" class="active">
                            <i class="fas fa-cog"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i class="fas fa-sign-out-alt"></i>
                            <span class="nav-text">Logout</span>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1>Account Settings</h1>
                <div class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span class="divider">/</span>
                    <span>Settings</span>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Profile Information</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" id="saveProfileBtn">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </div>
                
                <div class="service-card">
                    <div class="card-body">
                        <form id="profileForm">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                <div class="form-group">
                                    <label for="firstName">First Name</label>
                                    <input type="text" id="firstName" class="form-control" value="John" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="lastName">Last Name</label>
                                    <input type="text" id="lastName" class="form-control" value="Doe" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">Email Address</label>
                                    <input type="email" id="email" class="form-control" value="<EMAIL>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="tel" id="phone" class="form-control" value="+****************">
                                </div>
                                
                                <div class="form-group">
                                    <label for="company">Company</label>
                                    <input type="text" id="company" class="form-control" value="DigitalAce Solutions">
                                </div>
                                
                                <div class="form-group">
                                    <label for="timezone">Timezone</label>
                                    <select id="timezone" class="form-control select-control">
                                        <option value="EST">Eastern Standard Time (EST)</option>
                                        <option value="CST">Central Standard Time (CST)</option>
                                        <option value="MST">Mountain Standard Time (MST)</option>
                                        <option value="PST">Pacific Standard Time (PST)</option>
                                    </select>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Security Settings</h2>
                </div>
                
                <div class="service-card">
                    <div class="card-body">
                        <form id="securityForm">
                            <div class="form-group">
                                <label for="currentPassword">Current Password</label>
                                <input type="password" id="currentPassword" class="form-control" placeholder="Enter current password">
                            </div>
                            
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                                <div class="form-group">
                                    <label for="newPassword">New Password</label>
                                    <input type="password" id="newPassword" class="form-control" placeholder="Enter new password">
                                </div>
                                
                                <div class="form-group">
                                    <label for="confirmPassword">Confirm New Password</label>
                                    <input type="password" id="confirmPassword" class="form-control" placeholder="Confirm new password">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-key"></i> Update Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Notification Preferences</h2>
                </div>
                
                <div class="service-card">
                    <div class="card-body">
                        <div class="card-info">
                            <div class="info-item">
                                <span class="info-label">Email Notifications</span>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="info-item">
                                <span class="info-label">SMS Notifications</span>
                                <label class="switch">
                                    <input type="checkbox">
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="info-item">
                                <span class="info-label">GPS Alerts</span>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Service Updates</span>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Billing Reminders</span>
                                <label class="switch">
                                    <input type="checkbox" checked>
                                    <span class="slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Account Actions</h2>
                </div>
                
                <div class="service-card">
                    <div class="card-body">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                            <div>
                                <h4 style="margin-bottom: 10px; color: var(--text-color);">Export Data</h4>
                                <p style="margin-bottom: 15px; color: var(--text-secondary); font-size: 0.9rem;">Download all your account data and activity history.</p>
                                <button class="btn btn-outline">
                                    <i class="fas fa-download"></i> Export Data
                                </button>
                            </div>
                            
                            <div>
                                <h4 style="margin-bottom: 10px; color: var(--text-color);">Two-Factor Authentication</h4>
                                <p style="margin-bottom: 15px; color: var(--text-secondary); font-size: 0.9rem;">Add an extra layer of security to your account.</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-shield-alt"></i> Enable 2FA
                                </button>
                            </div>
                            
                            <div>
                                <h4 style="margin-bottom: 10px; color: #ef5350;">Delete Account</h4>
                                <p style="margin-bottom: 15px; color: var(--text-secondary); font-size: 0.9rem;">Permanently delete your account and all associated data.</p>
                                <button class="btn" style="background: #ef5350; color: white;">
                                    <i class="fas fa-trash"></i> Delete Account
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="script.js"></script>
    
    <style>
        /* Toggle Switch Styles */
        .switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: var(--transition);
            border-radius: 24px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: var(--transition);
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</body>
</html>
