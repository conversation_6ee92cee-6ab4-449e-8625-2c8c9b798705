<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment - DigitalAce</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>

<body>
    <!-- Responsive Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            <button class="toggle-sidebar" id="toggleSidebar">
                <i class="fas fa-bars"></i>
            </button>
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                <span>DigitalAce</span>
            </a>
        </div>

        <div class="nav-buttons">
            <button class="nav-btn user-btn">
                <i class="fas fa-user-circle"></i>
                <span><PERSON></span>
            </button>
            <button class="theme-toggle" id="themeToggle">
                <i class="fas fa-moon"></i>
            </button>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>
    </nav>

    <!-- Dashboard Layout -->
    <div class="dashboard-container" id="dashboardContainer">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User">
                <div class="user-info">
                    <div class="user-name">John Doe</div>
                    <div class="user-email"><EMAIL></div>
                </div>
            </div>

            <div class="nav-section">
                <div class="nav-title">Main</div>
                <ul class="nav-links">
                    <li><a href="index.html"><i class="fas fa-home"></i><span class="nav-text">Dashboard</span></a></li>
                    <li><a href="services.html"><i class="fas fa-cogs"></i><span class="nav-text">My
                                Services</span><span class="badge">3</span></a></li>
                    <li><a href="purchases.html"><i class="fas fa-shopping-cart"></i><span class="nav-text">My
                                Purchases</span></a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-title">GPS Tracking</div>
                <ul class="nav-links">
                    <li><a href="gps-devices.html"><i class="fas fa-map-marker-alt"></i><span class="nav-text">My
                                Devices</span><span class="badge">2</span></a></li>
                    <li><a href="gps-plans.html"><i class="fas fa-file-invoice-dollar"></i><span class="nav-text">Plans
                                & Billing</span></a></li>
                    <li><a href="gps-credentials.html"><i class="fas fa-key"></i><span
                                class="nav-text">Credentials</span></a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-title">Hosting</div>
                <ul class="nav-links">
                    <li><a href="hosting-accounts.html"><i class="fas fa-server"></i><span class="nav-text">My
                                Hosting</span><span class="badge">1</span></a></li>
                    <li><a href="hosting-plans.html"><i class="fas fa-box-open"></i><span class="nav-text">Hosting
                                Plans</span></a></li>
                    <li><a href="hosting-billing.html"><i class="fas fa-credit-card"></i><span
                                class="nav-text">Billing</span></a></li>
                </ul>
            </div>

            <div class="nav-section">
                <div class="nav-title">Account</div>
                <ul class="nav-links">
                    <li><a href="settings.html"><i class="fas fa-cog"></i><span class="nav-text">Settings</span></a>
                    </li>
                    <li><a href="#"><i class="fas fa-sign-out-alt"></i><span class="nav-text">Logout</span></a></li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1>Payment Checkout</h1>
                <div class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span class="divider">/</span>
                    <a href="purchases.html">Purchases</a>
                    <span class="divider">/</span>
                    <span>Payment</span>
                </div>
            </div>

            <div class="payment-layout">
                <!-- Order Summary Section -->
                <div class="dashboard-section">
                    <div class="section-header">
                        <h2>Order Summary</h2>
                        <div class="section-actions">
                            <button class="btn btn-outline" id="editCartBtn">
                                <i class="fas fa-edit"></i> Edit Cart
                            </button>
                        </div>
                    </div>

                    <!-- Order Items -->
                    <div class="service-card">
                        <div class="card-body">
                            <div class="order-items" id="orderItems">
                                <!-- Items will be populated by JavaScript -->
                            </div>

                            <!-- Order Totals -->
                            <div class="order-totals">
                                <div class="total-row subtotal-row">
                                    <span class="total-label">Subtotal:</span>
                                    <span class="total-value" id="subtotalAmount">$0.00</span>
                                </div>
                                <div class="total-row tax-row">
                                    <span class="total-label">Tax (18% GST):</span>
                                    <span class="total-value" id="taxAmount">$0.00</span>
                                </div>
                                <div class="total-row discount-row" id="discountRow" style="display: none;">
                                    <span class="total-label">Discount:</span>
                                    <span class="total-value discount" id="discountAmount">-$0.00</span>
                                </div>
                                <div class="total-row final-total">
                                    <span class="total-label">Total Amount:</span>
                                    <span class="total-value" id="finalAmount">$0.00</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Billing Information -->
                    <div class="dashboard-section">
                        <div class="section-header">
                            <h2>Billing Information</h2>
                        </div>

                        <div class="service-card">
                            <div class="card-body">
                                <form id="billingForm">
                                    <div
                                        style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                                        <div class="form-group">
                                            <label for="billingName">Full Name</label>
                                            <input type="text" id="billingName" class="form-control" value="John Doe"
                                                required>
                                        </div>

                                        <div class="form-group">
                                            <label for="billingEmail">Email Address</label>
                                            <input type="email" id="billingEmail" class="form-control"
                                                value="<EMAIL>" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="billingPhone">Phone Number</label>
                                            <input type="tel" id="billingPhone" class="form-control"
                                                value="+****************" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="billingCompany">Company (Optional)</label>
                                            <input type="text" id="billingCompany" class="form-control"
                                                value="DigitalAce Solutions">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="billingAddress">Address</label>
                                        <textarea id="billingAddress" class="form-control" rows="3"
                                            required>123 Business Street, Toronto, ON M5V 3A8, Canada</textarea>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Section -->
                <div class="payment-section">
                    <div class="service-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="card-title">
                                <h3>Payment Method</h3>
                                <p>Choose your preferred payment option</p>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- Payment Methods -->
                            <div class="payment-methods">
                                <div class="payment-method active" data-method="upi">
                                    <div class="method-icon">
                                        <i class="fas fa-mobile-alt"></i>
                                    </div>
                                    <div class="method-info">
                                        <h4>UPI Payment</h4>
                                        <p>Pay using UPI apps</p>
                                    </div>
                                    <div class="method-radio">
                                        <input type="radio" name="paymentMethod" value="upi" checked>
                                    </div>
                                </div>

                                <div class="payment-method" data-method="card">
                                    <div class="method-icon">
                                        <i class="fas fa-credit-card"></i>
                                    </div>
                                    <div class="method-info">
                                        <h4>Credit/Debit Card</h4>
                                        <p>Visa, Mastercard, etc.</p>
                                    </div>
                                    <div class="method-radio">
                                        <input type="radio" name="paymentMethod" value="card">
                                    </div>
                                </div>

                                <div class="payment-method" data-method="netbanking">
                                    <div class="method-icon">
                                        <i class="fas fa-university"></i>
                                    </div>
                                    <div class="method-info">
                                        <h4>Net Banking</h4>
                                        <p>Pay via your bank</p>
                                    </div>
                                    <div class="method-radio">
                                        <input type="radio" name="paymentMethod" value="netbanking">
                                    </div>
                                </div>
                            </div>

                            <!-- UPI QR Code Section -->
                            <div class="upi-section" id="upiSection">
                                <div class="upi-header">
                                    <h4>Scan QR Code to Pay</h4>
                                    <p>Use any UPI app to scan and pay</p>
                                </div>

                                <div class="qr-container">
                                    <div class="qr-code">
                                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=upi://pay?pa=digitalace@paytm%26pn=DigitalAce%20Solutions%26am=304000%26cu=INR%26tn=ORD-2023-001"
                                             alt="UPI Payment QR Code"
                                             style="width: 100%; height: 100%; object-fit: contain; border-radius: 10px;">
                                    </div>

                                    <div class="upi-details">
                                        <div class="upi-info">
                                            <span class="upi-label">UPI ID:</span>
                                            <span class="upi-value">digitalace@paytm</span>
                                        </div>
                                        <div class="upi-info">
                                            <span class="upi-label">Amount:</span>
                                            <span class="upi-value" id="upiAmount">₹3,04,000.00</span>
                                        </div>
                                        <div class="upi-info">
                                            <span class="upi-label">Reference:</span>
                                            <span class="upi-value" id="upiReference">ORD-2023-001</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="upi-apps">
                                    <p>Supported UPI Apps:</p>
                                    <div class="app-icons">
                                        <div class="app-icon">
                                            <i class="fab fa-google-pay"></i>
                                            <span>GPay</span>
                                        </div>
                                        <div class="app-icon">
                                            <i class="fas fa-mobile-alt"></i>
                                            <span>PhonePe</span>
                                        </div>
                                        <div class="app-icon">
                                            <i class="fas fa-mobile-alt"></i>
                                            <span>Paytm</span>
                                        </div>
                                        <div class="app-icon">
                                            <i class="fas fa-mobile-alt"></i>
                                            <span>BHIM</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Payment Confirmation Section -->
                                <div class="payment-confirmation">
                                    <div class="confirmation-header">
                                        <h4><i class="fas fa-shield-alt"></i> Payment Confirmation</h4>
                                        <p>After completing the payment, enter the transaction reference number below
                                        </p>
                                    </div>

                                    <div class="form-group">
                                        <label for="transactionReference">
                                            <i class="fas fa-receipt"></i> Transaction Reference Number *
                                        </label>
                                        <input type="text" id="transactionReference" class="form-control"
                                            placeholder="Enter UPI transaction ID (e.g., 123456789012)" required
                                            maxlength="50">
                                        <div class="field-help">
                                            <i class="fas fa-info-circle"></i>
                                            You can find this in your UPI app after successful payment
                                        </div>
                                        <div class="error-message" id="referenceError" style="display: none;">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Transaction reference number is required to confirm payment
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Actions -->
                            <div class="payment-actions">
                                <button class="btn btn-outline" onclick="window.history.back()">
                                    <i class="fas fa-arrow-left"></i> Back to Cart
                                </button>
                                <button class="btn btn-primary" id="confirmPaymentBtn">
                                    <i class="fas fa-lock"></i> Confirm Payment
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Order Summary Card -->
                    <div class="service-card order-summary-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-receipt"></i>
                            </div>
                            <div class="card-title">
                                <h3>Order Total</h3>
                                <p>Final amount to pay</p>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="summary-item">
                                <span>Items:</span>
                                <span id="summaryItems">3</span>
                            </div>
                            <div class="summary-item">
                                <span>Subtotal:</span>
                                <span id="summarySubtotal">$0.00</span>
                            </div>
                            <div class="summary-item">
                                <span>Tax:</span>
                                <span id="summaryTax">$0.00</span>
                            </div>
                            <div class="summary-total">
                                <span>Total:</span>
                                <span id="summaryTotal">$0.00</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
    <script src="payment.js"></script>
</body>

</html>