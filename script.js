// Theme toggle functionality
const themeToggle = document.getElementById('themeToggle');
const themeIcon = themeToggle.querySelector('i');
const body = document.body;

// Check for saved theme preference
const savedTheme = localStorage.getItem('theme');
if (savedTheme) {
    body.setAttribute('data-theme', savedTheme);
    themeIcon.className = savedTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
}

// Toggle theme
themeToggle.addEventListener('click', () => {
    const currentTheme = body.getAttribute('data-theme') || 'dark';
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    body.setAttribute('data-theme', newTheme);
    themeIcon.className = newTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
    
    // Save theme preference
    localStorage.setItem('theme', newTheme);
});

// Sidebar toggle functionality
const toggleSidebar = document.getElementById('toggleSidebar');
const dashboardContainer = document.getElementById('dashboardContainer');
const sidebar = document.getElementById('sidebar');

toggleSidebar.addEventListener('click', () => {
    dashboardContainer.classList.toggle('sidebar-collapsed');
});

// Mobile menu toggle
const menuToggle = document.getElementById('menuToggle');

menuToggle.addEventListener('click', () => {
    sidebar.classList.toggle('active');
});

// Service Modal functionality
const serviceModal = document.getElementById('serviceModal');
const newServiceBtn = document.getElementById('newServiceBtn');
const closeServiceModal = document.getElementById('closeServiceModal');
const cancelService = document.getElementById('cancelService');

function openServiceModal() {
    serviceModal.classList.add('active');
}

function closeServiceModalFunc() {
    serviceModal.classList.remove('active');
}

newServiceBtn.addEventListener('click', openServiceModal);
closeServiceModal.addEventListener('click', closeServiceModalFunc);
cancelService.addEventListener('click', closeServiceModalFunc);

// Close modal when clicking outside
window.addEventListener('click', (e) => {
    if (e.target === serviceModal) {
        closeServiceModalFunc();
    }
});

// Form submission
const serviceForm = document.getElementById('serviceForm');
const submitService = document.getElementById('submitService');

submitService.addEventListener('click', (e) => {
    e.preventDefault();
    // Form validation would go here
    alert('Service request submitted successfully!');
    serviceForm.reset();
    closeServiceModalFunc();
});

// Navigation between sections
const navLinks = document.querySelectorAll('.nav-links a');
const pageContents = document.querySelectorAll('.page-content');

navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        const targetSection = link.getAttribute('data-section');
        
        // Hide all content sections
        pageContents.forEach(content => {
            content.style.display = 'none';
        });
        
        // Show target section
        const targetElement = document.getElementById(targetSection);
        if (targetElement) {
            targetElement.style.display = 'block';
        }
        
        // Update active link
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
        
        // Close sidebar on mobile after selection
        if (window.innerWidth < 992) {
            sidebar.classList.remove('active');
        }
    });
});

// Initialize charts
function initCharts() {
    // This would initialize Chart.js charts on the dashboard
    // For simplicity, we're not implementing actual charts in this demo
}

// Initialize the dashboard
window.addEventListener('DOMContentLoaded', () => {
    // Hide all sections except overview
    pageContents.forEach(content => {
        if (content.id !== 'overview') {
            content.style.display = 'none';
        }
    });
    
    initCharts();
});

// Additional utility functions for dashboard management
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Handle responsive sidebar behavior
function handleResize() {
    if (window.innerWidth > 992) {
        sidebar.classList.remove('active');
    }
}

window.addEventListener('resize', handleResize);

// Smooth scrolling for internal links
function smoothScroll(target) {
    const element = document.querySelector(target);
    if (element) {
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// Enhanced form validation
function validateForm(formElement) {
    const requiredFields = formElement.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });
    
    return isValid;
}

// Dynamic content loading simulation
function loadSectionContent(sectionId) {
    const section = document.getElementById(sectionId);
    if (!section) return;
    
    // Add loading state
    section.innerHTML = '<div class="loading">Loading...</div>';
    
    // Simulate API call
    setTimeout(() => {
        // This would be replaced with actual content loading
        section.innerHTML = generateSectionContent(sectionId);
    }, 500);
}

function generateSectionContent(sectionId) {
    // This function would generate appropriate content for each section
    // For now, return a placeholder
    return `
        <div class="page-header">
            <h1>${sectionId.charAt(0).toUpperCase() + sectionId.slice(1)}</h1>
            <div class="breadcrumb">
                <a href="#">Home</a>
                <span class="divider">/</span>
                <span>${sectionId}</span>
            </div>
        </div>
        <div class="section-content">
            <p>Content for ${sectionId} section will be loaded here.</p>
        </div>
    `;
}

// Export functions for use in other scripts
window.DashboardUtils = {
    showNotification,
    smoothScroll,
    validateForm,
    loadSectionContent
};
