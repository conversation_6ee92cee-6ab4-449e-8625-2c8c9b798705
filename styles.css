:root {
    /* Theme colors - Matching PrintHub */
    --primary: #7E57C2;
    --primary-dark: #5E35B1;
    --secondary: #26A69A;
    --accent: #FFCA28;
    --dark: #263238;
    --light: #f5f7fa;
    --gray: #78909C;
    --light-gray: #ECEFF1;
    --card-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    --nav-height: 80px;
    
    /* Theme-specific variables */
    --bg-gradient: linear-gradient(135deg, #2c1b47 0%, #1c2331 100%);
    --header-bg: rgba(38, 50, 56, 0.95);
    --card-bg: rgba(38, 50, 56, 0.8);
    --text-color: #f5f7fa;
    --text-secondary: #CFD8DC;
    --border-color: rgba(126, 87, 194, 0.3);
    --footer-bg: rgba(38, 50, 56, 0.95);
    
    /* Dashboard specific */
    --sidebar-width: 280px;
    --sidebar-collapsed: 80px;
}

/* Light Theme Overrides */
[data-theme="light"] {
    --bg-gradient: linear-gradient(135deg, #f0f2f5 0%, #e4e6e9 100%);
    --header-bg: rgba(255, 255, 255, 0.95);
    --card-bg: rgba(255, 255, 255, 0.9);
    --text-color: #263238;
    --text-secondary: #546E7A;
    --border-color: rgba(206, 212, 218, 0.5);
    --footer-bg: rgba(255, 255, 255, 0.95);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', 'Segoe UI', sans-serif;
}

body {
    background: var(--bg-gradient);
    min-height: 100vh;
    color: var(--text-color);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    padding-top: var(--nav-height);
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background: var(--header-bg);
    backdrop-filter: blur(10px);
    height: calc(100vh - var(--nav-height));
    position: fixed;
    left: 0;
    top: var(--nav-height);
    z-index: 900;
    overflow-y: auto;
    border-right: 1px solid var(--border-color);
    transition: var(--transition);
    padding: 25px 0;
}

.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed);
}

.sidebar-header {
    padding: 0 25px 20px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.sidebar-header img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--accent);
}

.user-info {
    flex: 1;
    min-width: 0;
    overflow: hidden;
}

.user-name {
    font-weight: 600;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-email {
    font-size: 0.85rem;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-collapsed .user-info {
    display: none;
}

.nav-section {
    margin-bottom: 25px;
}

.nav-title {
    padding: 10px 25px;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.sidebar-collapsed .nav-title {
    display: none;
}

.nav-links {
    list-style: none;
}

.nav-links li a {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 12px 25px;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-links li a:hover, .nav-links li a.active {
    background: rgba(126, 87, 194, 0.2);
    color: var(--accent);
}

.nav-links li a i {
    width: 24px;
    text-align: center;
    font-size: 1.2rem;
}

.nav-links li a .nav-text {
    flex: 1;
}

.sidebar-collapsed .nav-text {
    display: none;
}

.nav-links li a .badge {
    background: var(--primary);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.sidebar-collapsed .badge {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 30px;
    transition: var(--transition);
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed);
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 2.2rem;
    background: linear-gradient(to right, var(--accent), var(--primary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.breadcrumb {
    display: flex;
    gap: 10px;
    color: var(--text-secondary);
    font-size: 0.95rem;
}

.breadcrumb a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
}

.breadcrumb a:hover {
    color: var(--accent);
}

.breadcrumb .divider {
    opacity: 0.6;
}

/* Dashboard Cards */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.stat-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.stat-title {
    font-size: 1rem;
    color: var(--text-secondary);
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: rgba(126, 87, 194, 0.2);
    color: var(--primary);
}

.stat-value {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    color: var(--secondary);
}

.stat-trend.down {
    color: #ef5350;
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 40px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.section-header h2 {
    font-size: 1.8rem;
    color: var(--text-color);
    position: relative;
    padding-bottom: 10px;
}

.section-header h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--accent);
    border-radius: 3px;
}

.section-actions {
    display: flex;
    gap: 15px;
}

.btn {
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.95rem;
    border: none;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: 0 5px 15px rgba(126, 87, 194, 0.3);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--text-color);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 7px 20px rgba(126, 87, 194, 0.4);
}

.btn i {
    font-size: 1rem;
}

/* Data Tables */
.data-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
}

.data-table th {
    background: rgba(126, 87, 194, 0.2);
    padding: 18px 20px;
    text-align: left;
    font-weight: 600;
    color: var(--accent);
    text-transform: uppercase;
    font-size: 0.9rem;
    letter-spacing: 0.5px;
}

.data-table td {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    font-size: 0.95rem;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover td {
    background: rgba(126, 87, 194, 0.1);
}

.status-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(38, 166, 154, 0.2);
    color: var(--secondary);
}

.status-badge.pending {
    background: rgba(255, 202, 40, 0.2);
    color: var(--accent);
}

.status-badge.expired {
    background: rgba(239, 83, 80, 0.2);
    color: #ef5350;
}

.action-cell {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(126, 87, 194, 0.1);
    color: var(--text-color);
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.action-btn:hover {
    background: var(--primary);
    color: white;
    transform: translateY(-3px);
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 25px;
}

.service-card {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.service-card:hover {
    transform: translateY(-5px);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    background: rgba(126, 87, 194, 0.2);
    color: var(--primary);
}

.card-title {
    flex: 1;
}

.card-title h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.card-title p {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.card-body {
    padding: 20px;
}

.card-info {
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px dashed var(--border-color);
    font-size: 0.95rem;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: var(--text-secondary);
}

.card-footer {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: space-between;
}

/* Form Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-color);
    transform: translateY(20px);
    transition: var(--transition);
    max-height: 90vh;
    overflow-y: auto;
}

.modal.active .modal-content {
    transform: translateY(0);
}

.modal-header {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    font-size: 1.5rem;
}

.close-modal {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1.5rem;
    cursor: pointer;
    transition: var(--transition);
}

.close-modal:hover {
    color: var(--accent);
    transform: rotate(90deg);
}

.modal-body {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 14px 18px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    background: transparent;
    color: var(--text-color);
    font-size: 1rem;
    transition: var(--transition);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 3px rgba(255, 202, 40, 0.2);
}

.form-control::placeholder {
    color: var(--text-secondary);
}

.select-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2378909C' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

/* Responsive Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: var(--nav-height);
    background: var(--header-bg);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 5%;
    z-index: 1000;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-color);
    text-decoration: none;
}

.logo i {
    color: var(--accent);
}

.nav-buttons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.nav-btn {
    padding: 10px 25px;
    border-radius: 30px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 1rem;
}

.user-btn {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.theme-toggle {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.5rem;
    cursor: pointer;
    margin-left: 15px;
    transition: var(--transition);
}

.theme-toggle:hover {
    color: var(--accent);
    transform: rotate(20deg);
}

.menu-toggle {
    display: none;
    font-size: 1.8rem;
    cursor: pointer;
    color: var(--text-color);
}

.toggle-sidebar {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.5rem;
    cursor: pointer;
    margin-right: 20px;
    transition: var(--transition);
}

.toggle-sidebar:hover {
    color: var(--accent);
}

/* Responsive Design */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
        width: var(--sidebar-width);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-collapsed .sidebar {
        width: var(--sidebar-width);
        transform: translateX(-100%);
    }

    .sidebar-collapsed .sidebar.active {
        transform: translateX(0);
    }

    .sidebar-collapsed .main-content {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
    }
}

@media (max-width: 768px) {
    :root {
        --nav-height: 70px;
    }

    .main-content {
        padding: 20px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .section-actions {
        width: 100%;
        flex-wrap: wrap;
    }

    .data-table {
        display: block;
        overflow-x: auto;
    }
}

@media (max-width: 480px) {
    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .cards-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
    }
}

/* Payment Page Styles */
.payment-layout {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    align-items: start;
}

.payment-section {
    position: sticky;
    top: 100px;
}

.order-items {
    margin-bottom: 25px;
}

.order-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.order-item:last-child {
    border-bottom: none;
}

.item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background: rgba(126, 87, 194, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--primary);
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 5px;
}

.item-description {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.item-quantity {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 8px 0;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid var(--border-color);
    background: transparent;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-color);
}

.quantity-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.quantity-input {
    width: 50px;
    text-align: center;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 5px;
    background: transparent;
    color: var(--text-color);
}

.item-pricing {
    text-align: right;
    min-width: 100px;
}

.item-rate {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-bottom: 5px;
}

.item-total {
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--primary);
}

.order-totals {
    border-top: 2px solid var(--border-color);
    padding-top: 20px;
    margin-top: 20px;
}

.total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    font-size: 1rem;
}

.total-row.final-total {
    border-top: 1px solid var(--border-color);
    margin-top: 10px;
    padding-top: 15px;
    font-size: 1.2rem;
    font-weight: 700;
}

.total-row.final-total .total-value {
    color: var(--primary);
    font-size: 1.4rem;
}

.total-value.discount {
    color: var(--secondary);
}

.payment-methods {
    margin-bottom: 25px;
}

.payment-method {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 2px solid var(--border-color);
    border-radius: 10px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: var(--transition);
}

.payment-method:hover {
    border-color: var(--primary);
    background: rgba(126, 87, 194, 0.05);
}

.payment-method.active {
    border-color: var(--primary);
    background: rgba(126, 87, 194, 0.1);
}

.method-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: rgba(126, 87, 194, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: var(--primary);
}

.method-info h4 {
    margin-bottom: 5px;
    font-size: 1rem;
}

.method-info p {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin: 0;
}

.method-radio {
    margin-left: auto;
}

.method-radio input[type="radio"] {
    width: 20px;
    height: 20px;
    accent-color: var(--primary);
}

.upi-section {
    background: rgba(126, 87, 194, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 25px;
}

.upi-header {
    text-align: center;
    margin-bottom: 25px;
}

.upi-header h4 {
    margin-bottom: 8px;
    color: var(--text-color);
}

.upi-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.qr-container {
    display: flex;
    gap: 25px;
    align-items: center;
    margin-bottom: 25px;
}

.qr-code {
    width: 200px;
    height: 200px;
    border: 2px solid var(--border-color);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    flex-shrink: 0;
}

.qr-placeholder {
    text-align: center;
    color: var(--text-secondary);
}

.qr-placeholder i {
    font-size: 3rem;
    margin-bottom: 10px;
    display: block;
}

.upi-details {
    flex: 1;
}

.upi-info {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px dashed var(--border-color);
}

.upi-info:last-child {
    border-bottom: none;
}

.upi-label {
    font-weight: 500;
    color: var(--text-secondary);
}

.upi-value {
    font-weight: 600;
    color: var(--text-color);
}

.upi-apps {
    text-align: center;
}

.upi-apps p {
    margin-bottom: 15px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.app-icons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.app-icon {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    padding: 10px;
    border-radius: 8px;
    background: rgba(126, 87, 194, 0.1);
    min-width: 60px;
    transition: var(--transition);
}

.app-icon:hover {
    background: rgba(126, 87, 194, 0.2);
    transform: translateY(-2px);
}

.app-icon i {
    font-size: 1.5rem;
    color: var(--primary);
}

.app-icon span {
    font-size: 0.8rem;
    color: var(--text-color);
    font-weight: 500;
}

.payment-confirmation {
    margin-top: 25px;
    padding: 20px;
    background: rgba(255, 202, 40, 0.1);
    border: 2px solid rgba(255, 202, 40, 0.3);
    border-radius: 12px;
}

.confirmation-header {
    text-align: center;
    margin-bottom: 20px;
}

.confirmation-header h4 {
    color: var(--text-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.confirmation-header h4 i {
    color: var(--accent);
}

.confirmation-header p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-color);
}

.form-group label i {
    color: var(--accent);
}

.field-help {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 6px;
    font-size: 0.8rem;
    color: var(--text-secondary);
    line-height: 1.3;
}

.field-help i {
    color: var(--secondary);
    font-size: 0.75rem;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(239, 83, 80, 0.1);
    border: 1px solid rgba(239, 83, 80, 0.3);
    border-radius: 6px;
    color: #ef5350;
    font-size: 0.85rem;
    animation: shake 0.3s ease-in-out;
}

.error-message i {
    color: #ef5350;
}

.form-control.error {
    border-color: #ef5350;
    box-shadow: 0 0 0 3px rgba(239, 83, 80, 0.2);
}

.form-control.success {
    border-color: var(--secondary);
    box-shadow: 0 0 0 3px rgba(38, 166, 154, 0.2);
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.payment-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.payment-actions .btn {
    flex: 1;
}

.order-summary-card {
    margin-top: 20px;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    font-size: 0.95rem;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    padding: 15px 0 0;
    margin-top: 15px;
    border-top: 2px solid var(--border-color);
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary);
}

/* Responsive Design for Payment Page */
@media (max-width: 1200px) {
    .payment-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .payment-section {
        position: static;
        order: -1; /* Move payment section to top on smaller screens */
    }

    .qr-container {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .qr-code {
        margin: 0 auto;
        width: 180px;
        height: 180px;
    }

    .upi-details {
        max-width: 300px;
        margin: 0 auto;
    }
}

@media (max-width: 992px) {
    /* Adjust payment layout for tablets */
    .payment-methods {
        display: grid;
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .payment-method {
        padding: 12px;
    }

    .method-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .method-info h4 {
        font-size: 0.95rem;
    }

    .method-info p {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    /* Mobile optimizations */
    .main-content {
        padding: 15px;
    }

    .page-header {
        margin-bottom: 20px;
    }

    .page-header h1 {
        font-size: 1.8rem;
    }

    .breadcrumb {
        font-size: 0.85rem;
        flex-wrap: wrap;
    }

    /* Order items mobile layout */
    .order-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
        padding: 15px;
        background: rgba(126, 87, 194, 0.05);
        border-radius: 10px;
        margin-bottom: 10px;
        border-bottom: none;
    }

    .order-item:last-child {
        margin-bottom: 0;
    }

    .item-image {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .item-details {
        width: 100%;
    }

    .item-name {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .item-description {
        font-size: 0.85rem;
        line-height: 1.4;
        margin-bottom: 10px;
    }

    .item-quantity {
        justify-content: space-between;
        width: 100%;
        margin: 10px 0;
    }

    .quantity-controls {
        gap: 12px;
    }

    .quantity-btn {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .quantity-input {
        width: 60px;
        height: 35px;
        font-size: 1rem;
        text-align: center;
    }

    .item-pricing {
        text-align: left;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: rgba(126, 87, 194, 0.1);
        padding: 10px;
        border-radius: 8px;
    }

    .item-rate {
        margin-bottom: 0;
        font-size: 0.85rem;
    }

    .item-total {
        font-size: 1.2rem;
    }

    /* Order totals mobile */
    .order-totals {
        background: var(--card-bg);
        padding: 20px;
        border-radius: 10px;
        margin-top: 15px;
    }

    .total-row {
        padding: 10px 0;
        font-size: 1rem;
    }

    .total-row.final-total {
        padding-top: 15px;
        font-size: 1.3rem;
    }

    .total-row.final-total .total-value {
        font-size: 1.5rem;
    }

    /* Payment methods mobile */
    .payment-method {
        padding: 15px;
        margin-bottom: 12px;
    }

    .method-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .method-info {
        flex: 1;
    }

    .method-info h4 {
        font-size: 1rem;
        margin-bottom: 4px;
    }

    .method-info p {
        font-size: 0.85rem;
    }

    /* UPI section mobile */
    .upi-section {
        padding: 20px 15px;
    }

    .upi-header h4 {
        font-size: 1.1rem;
    }

    .qr-code {
        width: 160px;
        height: 160px;
    }

    .qr-placeholder i {
        font-size: 2.5rem;
    }

    .upi-details {
        width: 100%;
    }

    .upi-info {
        padding: 12px 0;
        font-size: 0.95rem;
    }

    .upi-label {
        font-size: 0.9rem;
    }

    .upi-value {
        font-size: 0.95rem;
        word-break: break-all;
    }

    /* UPI apps mobile */
    .app-icons {
        gap: 15px;
        justify-content: space-around;
    }

    .app-icon {
        min-width: 55px;
        padding: 10px 8px;
    }

    .app-icon i {
        font-size: 1.3rem;
    }

    .app-icon span {
        font-size: 0.75rem;
    }

    /* Payment actions mobile */
    .payment-actions {
        flex-direction: column;
        gap: 12px;
        margin-top: 20px;
    }

    .payment-actions .btn {
        padding: 15px 20px;
        font-size: 1rem;
    }

    /* Order summary card mobile */
    .order-summary-card {
        margin-top: 15px;
    }

    .summary-item {
        padding: 10px 0;
        font-size: 1rem;
    }

    .summary-total {
        font-size: 1.3rem;
        padding: 15px 0 0;
    }

    /* Billing form mobile */
    .form-group {
        margin-bottom: 15px;
    }

    .form-control {
        padding: 12px 15px;
        font-size: 1rem;
    }

    .form-group label {
        font-size: 0.95rem;
        margin-bottom: 6px;
    }
}

@media (max-width: 480px) {
    /* Extra small mobile devices */
    .main-content {
        padding: 10px;
    }

    .page-header h1 {
        font-size: 1.6rem;
        line-height: 1.2;
    }

    .breadcrumb {
        font-size: 0.8rem;
    }

    /* Compact order items */
    .order-item {
        padding: 12px;
        gap: 10px;
    }

    .item-image {
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }

    .item-name {
        font-size: 0.95rem;
        line-height: 1.3;
    }

    .item-description {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    .item-quantity {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .quantity-controls {
        gap: 10px;
    }

    .quantity-btn {
        width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .quantity-input {
        width: 50px;
        height: 32px;
        font-size: 0.9rem;
    }

    .item-pricing {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
        padding: 8px;
    }

    .item-total {
        font-size: 1.1rem;
    }

    /* Compact totals */
    .order-totals {
        padding: 15px;
    }

    .total-row {
        padding: 8px 0;
        font-size: 0.95rem;
    }

    .total-row.final-total {
        font-size: 1.2rem;
    }

    .total-row.final-total .total-value {
        font-size: 1.3rem;
    }

    /* Compact payment methods */
    .payment-method {
        padding: 12px;
        gap: 12px;
    }

    .method-icon {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .method-info h4 {
        font-size: 0.95rem;
    }

    .method-info p {
        font-size: 0.8rem;
    }

    /* Compact UPI section */
    .upi-section {
        padding: 15px 10px;
    }

    /* Payment confirmation mobile */
    .payment-confirmation {
        margin-top: 20px;
        padding: 15px;
    }

    .confirmation-header h4 {
        font-size: 1rem;
        flex-direction: column;
        gap: 5px;
    }

    .confirmation-header p {
        font-size: 0.85rem;
    }

    .field-help {
        font-size: 0.75rem;
    }

    .error-message {
        font-size: 0.8rem;
        padding: 6px 10px;
    }

    /* Mobile input optimization */
    #transactionReference {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 12px 15px;
    }

    .upi-header h4 {
        font-size: 1rem;
    }

    .upi-header p {
        font-size: 0.85rem;
    }

    .qr-code {
        width: 140px;
        height: 140px;
    }

    .qr-placeholder i {
        font-size: 2rem;
        margin-bottom: 8px;
    }

    .qr-placeholder p {
        font-size: 0.85rem;
    }

    .qr-placeholder small {
        font-size: 0.65rem;
    }

    .upi-info {
        padding: 10px 0;
        font-size: 0.9rem;
    }

    .upi-label {
        font-size: 0.85rem;
    }

    .upi-value {
        font-size: 0.9rem;
    }

    /* Compact app icons */
    .app-icons {
        gap: 12px;
    }

    .app-icon {
        min-width: 50px;
        padding: 8px 6px;
    }

    .app-icon i {
        font-size: 1.2rem;
    }

    .app-icon span {
        font-size: 0.7rem;
    }

    /* Compact buttons */
    .payment-actions .btn {
        padding: 12px 16px;
        font-size: 0.95rem;
    }

    /* Compact summary */
    .summary-item {
        padding: 8px 0;
        font-size: 0.95rem;
    }

    .summary-total {
        font-size: 1.2rem;
    }

    /* Compact forms */
    .form-control {
        padding: 10px 12px;
        font-size: 0.95rem;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    /* Section headers */
    .section-header h2 {
        font-size: 1.4rem;
    }

    .section-actions {
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .section-actions .btn {
        width: 100%;
        justify-content: center;
    }

    /* Card headers */
    .card-title h3 {
        font-size: 1rem;
    }

    .card-title p {
        font-size: 0.8rem;
    }

    /* Improve touch targets */
    .quantity-btn,
    .action-btn,
    .btn {
        min-height: 44px;
        min-width: 44px;
    }

    /* Better spacing for readability */
    .dashboard-section {
        margin-bottom: 25px;
    }

    .service-card {
        margin-bottom: 15px;
    }

    /* Optimize text for small screens */
    .nav-text {
        font-size: 0.9rem;
    }

    .badge {
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
    }
}

@media (max-width: 360px) {
    /* Very small screens */
    .main-content {
        padding: 8px;
    }

    .page-header h1 {
        font-size: 1.4rem;
    }

    .qr-code {
        width: 120px;
        height: 120px;
    }

    .app-icons {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .app-icon {
        min-width: auto;
        width: 100%;
    }

    .payment-actions {
        gap: 10px;
    }

    .order-item {
        padding: 10px;
    }

    .item-pricing {
        padding: 6px;
    }
}
