<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Services - DigitalAce</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Responsive Navigation Bar -->
    <nav class="navbar">
        <div class="nav-left">
            <button class="toggle-sidebar" id="toggleSidebar">
                <i class="fas fa-bars"></i>
            </button>
            <a href="index.html" class="logo">
                <i class="fas fa-rocket"></i>
                <span>DigitalAce</span>
            </a>
        </div>
        
        <div class="nav-buttons">
            <button class="nav-btn user-btn">
                <i class="fas fa-user-circle"></i>
                <span>John Doe</span>
            </button>
            <button class="theme-toggle" id="themeToggle">
                <i class="fas fa-moon"></i>
            </button>
            <div class="menu-toggle" id="menuToggle">
                <i class="fas fa-ellipsis-v"></i>
            </div>
        </div>
    </nav>

    <!-- Dashboard Layout -->
    <div class="dashboard-container" id="dashboardContainer">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User">
                <div class="user-info">
                    <div class="user-name">John Doe</div>
                    <div class="user-email"><EMAIL></div>
                </div>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Main</div>
                <ul class="nav-links">
                    <li>
                        <a href="index.html">
                            <i class="fas fa-home"></i>
                            <span class="nav-text">Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="services.html" class="active">
                            <i class="fas fa-cogs"></i>
                            <span class="nav-text">My Services</span>
                            <span class="badge">3</span>
                        </a>
                    </li>
                    <li>
                        <a href="purchases.html">
                            <i class="fas fa-shopping-cart"></i>
                            <span class="nav-text">My Purchases</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">GPS Tracking</div>
                <ul class="nav-links">
                    <li>
                        <a href="gps-devices.html">
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="nav-text">My Devices</span>
                            <span class="badge">2</span>
                        </a>
                    </li>
                    <li>
                        <a href="gps-plans.html">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span class="nav-text">Plans & Billing</span>
                        </a>
                    </li>
                    <li>
                        <a href="gps-credentials.html">
                            <i class="fas fa-key"></i>
                            <span class="nav-text">Credentials</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Hosting</div>
                <ul class="nav-links">
                    <li>
                        <a href="hosting-accounts.html">
                            <i class="fas fa-server"></i>
                            <span class="nav-text">My Hosting</span>
                            <span class="badge">1</span>
                        </a>
                    </li>
                    <li>
                        <a href="hosting-plans.html">
                            <i class="fas fa-box-open"></i>
                            <span class="nav-text">Hosting Plans</span>
                        </a>
                    </li>
                    <li>
                        <a href="hosting-billing.html">
                            <i class="fas fa-credit-card"></i>
                            <span class="nav-text">Billing</span>
                        </a>
                    </li>
                </ul>
            </div>
            
            <div class="nav-section">
                <div class="nav-title">Account</div>
                <ul class="nav-links">
                    <li>
                        <a href="settings.html">
                            <i class="fas fa-cog"></i>
                            <span class="nav-text">Settings</span>
                        </a>
                    </li>
                    <li>
                        <a href="#">
                            <i class="fas fa-sign-out-alt"></i>
                            <span class="nav-text">Logout</span>
                        </a>
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <div class="page-header">
                <h1>My Services</h1>
                <div class="breadcrumb">
                    <a href="index.html">Home</a>
                    <span class="divider">/</span>
                    <span>My Services</span>
                </div>
            </div>
            
            <div class="dashboard-stats">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Active Services</div>
                        <div class="stat-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                    </div>
                    <div class="stat-value">3</div>
                    <div class="stat-trend">
                        <i class="fas fa-arrow-up"></i>
                        <span>1 new this month</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Pending Requests</div>
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                    <div class="stat-value">2</div>
                    <div class="stat-trend">
                        <i class="fas fa-hourglass-half"></i>
                        <span>Awaiting approval</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Completed Projects</div>
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value">12</div>
                    <div class="stat-trend">
                        <i class="fas fa-trophy"></i>
                        <span>This year</span>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Investment</div>
                        <div class="stat-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value">₹7,01,375</div>
                    <div class="stat-trend">
                        <i class="fas fa-chart-line"></i>
                        <span>ROI: 340%</span>
                    </div>
                </div>
            </div>
            
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Active Services</h2>
                    <div class="section-actions">
                        <button class="btn btn-outline">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <button class="btn btn-primary" id="newServiceBtn">
                            <i class="fas fa-plus"></i> Request Service
                        </button>
                    </div>
                </div>
                
                <div class="cards-grid">
                    <div class="service-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-laptop-code"></i>
                            </div>
                            <div class="card-title">
                                <h3>Website Development</h3>
                                <p>E-commerce Platform</p>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="card-info">
                                <div class="info-item">
                                    <span class="info-label">Status</span>
                                    <span class="status-badge active">In Progress</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Progress</span>
                                    <span>75%</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Deadline</span>
                                    <span>Jul 15, 2023</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Developer</span>
                                    <span>Sarah Johnson</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-outline">View Details</button>
                            <button class="btn btn-primary">Contact Dev</button>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="card-title">
                                <h3>Mobile App</h3>
                                <p>iOS & Android App</p>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="card-info">
                                <div class="info-item">
                                    <span class="info-label">Status</span>
                                    <span class="status-badge pending">Pending</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Progress</span>
                                    <span>0%</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Estimated Start</span>
                                    <span>Jul 20, 2023</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Developer</span>
                                    <span>Pending Assignment</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-outline">View Details</button>
                            <button class="btn btn-primary">Edit Request</button>
                        </div>
                    </div>
                    
                    <div class="service-card">
                        <div class="card-header">
                            <div class="card-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="card-title">
                                <h3>SEO Optimization</h3>
                                <p>Complete SEO Package</p>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="card-info">
                                <div class="info-item">
                                    <span class="info-label">Status</span>
                                    <span class="status-badge active">In Progress</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Progress</span>
                                    <span>45%</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Deadline</span>
                                    <span>Aug 30, 2023</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Specialist</span>
                                    <span>Michael Chen</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer">
                            <button class="btn btn-outline">View Report</button>
                            <button class="btn btn-primary">Contact SEO</button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Service Request Modal -->
    <div class="modal" id="serviceModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>New Service Request</h3>
                <button class="close-modal" id="closeServiceModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="serviceForm">
                    <div class="form-group">
                        <label for="serviceType">Service Type</label>
                        <select id="serviceType" class="form-control select-control" required>
                            <option value="">Select a service</option>
                            <option value="web-dev">Web Development</option>
                            <option value="app-dev">Mobile App Development</option>
                            <option value="seo">SEO Optimization</option>
                            <option value="graphic">Graphic Design</option>
                            <option value="video">Video Production</option>
                            <option value="social">Social Media Management</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="serviceName">Service Name</label>
                        <input type="text" id="serviceName" class="form-control" placeholder="Enter service name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="serviceDescription">Description</label>
                        <textarea id="serviceDescription" class="form-control" rows="4" placeholder="Describe your service requirements" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="priority">Priority</label>
                        <select id="priority" class="form-control select-control" required>
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="attachment">Attachments (Optional)</label>
                        <input type="file" id="attachment" class="form-control">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" id="cancelService">Cancel</button>
                <button class="btn btn-primary" id="submitService">Submit Request</button>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
